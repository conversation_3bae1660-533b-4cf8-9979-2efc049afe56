#!/bin/bash

# AI Crawler Guard VPS Fix Script
# This script fixes the DB_URI environment variable issue

echo "🚀 AI Crawler Guard VPS Fix Script"
echo "=================================="

# Navigate to the backend directory
echo "📁 Navigating to backend directory..."
cd /datasets/_deepnote_work/ai-crawler-guard/Backend/subscription-tracker

# Check current directory
echo "Current directory: $(pwd)"

# Create the environment file
echo "🔧 Creating .env.development.local file..."
cat > .env.development.local << 'EOF'
NODE_ENV=development
PORT=3000

# Database Configuration
DB_URI="postgresql://ai-crawler-guard_owner:<EMAIL>/ai-crawler-guard?sslmode=require"

# Server Configuration
SERVER_URL="http://localhost:3000"

# Payment Gateway (Test Keys)
RAZORPAY_KEY_ID="rzp_test_your_test_key_id"
RAZORPAY_KEY_SECRET="your_test_key_secret"

# WordPress Configuration
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false
WP_ENVIRONMENT=development
WORDPRESS_SITE_URL=https://creativeinteriorsstudio.com

# JWT Authentication
JWT_SECRET="vps_development_jwt_secret_key"
JWT_EXPIRES_IN="1d"

# ArcJet Security
ARCJET_KEY="ajkey_01jv2k26vvercv3v13k2b9kn1d"
ARCJET_ENV="development"

# Upstash Queue Service
QSTASH_URL="http://127.0.0.1:8080"
QSTASH_TOKEN="eyJVc2VySUQiOiJkZWZhdWx0VXNlciIsIlBhc3N3b3JkIjoiZGVmYXVsdFBhc3N3b3JkIn0="
QSTASH_CURRENT_SIGNING_KEY="sig_development_key"
QSTASH_NEXT_SIGNING_KEY="sig_development_next_key"

# Email Service
EMAIL_PASSWORD="ysbb ohjv ljbt ttcm"

# Cloudflare Configuration
CLOUDFLARE_WORKER_URL=https://crawlguard-api-prod.crawlguard-api.workers.dev
CLOUDFLARE_CUSTOM_DOMAIN=https://api.creativeinteriorsstudio.com
CLOUDFLARE_ACCOUNT_ID=eb2e0a0f169c14046bc5f6b9946ce4e2
CLOUDFLARE_API_TOKEN=****************************************
CLOUDFLARE_ZONE_ID=20142c7575d785e9d93b316eb9fbbd46

# API Configuration
API_BASE_URL=https://api.creativeinteriorsstudio.com/v1
API_VERSION=1.0.0
API_TIMEOUT=30000
ENVIRONMENT=development

# Test Configuration
test_site=https://blogging-website-s.netlify.app/
EOF

# Verify the file was created
if [ -f ".env.development.local" ]; then
    echo "✅ Environment file created successfully!"
    echo "📋 File size: $(wc -l < .env.development.local) lines"
    echo "🔍 DB_URI check:"
    grep "DB_URI" .env.development.local
else
    echo "❌ Failed to create environment file!"
    exit 1
fi

# Set NODE_ENV
export NODE_ENV=development
echo "🔧 NODE_ENV set to: $NODE_ENV"

# Check if pnpm is available
if command -v pnpm &> /dev/null; then
    echo "✅ pnpm found"
    PKG_MANAGER="pnpm"
elif command -v npm &> /dev/null; then
    echo "✅ npm found"
    PKG_MANAGER="npm"
else
    echo "❌ No package manager found!"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    $PKG_MANAGER install
fi

echo ""
echo "🚀 Starting the server..."
echo "   Environment: $NODE_ENV"
echo "   Package Manager: $PKG_MANAGER"
echo ""

# Start the server
if [ "$PKG_MANAGER" = "pnpm" ]; then
    pnpm dev
else
    npm run dev
fi
