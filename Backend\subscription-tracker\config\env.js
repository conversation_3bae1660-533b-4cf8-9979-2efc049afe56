import { config } from "dotenv";
import { existsSync } from "fs";
import { join } from "path";

// Determine the environment
const nodeEnv = process.env.NODE_ENV || 'development';

// Try multiple environment file patterns
const envFiles = [
    `.env.${nodeEnv}.local`,
    `.env.local`,
    `.env.${nodeEnv}`,
    `.env`
];

// Load the first existing environment file
let envLoaded = false;
for (const envFile of envFiles) {
    if (existsSync(envFile)) {
        console.log(`🔧 Loading environment from: ${envFile}`);
        config({ path: envFile });
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    console.warn(`⚠️  No environment file found. Tried: ${envFiles.join(', ')}`);
    console.log('📋 Available files in current directory:');
    try {
        const fs = await import('fs');
        const files = fs.readdirSync('.').filter(f => f.startsWith('.env'));
        console.log(files.length > 0 ? files.join(', ') : 'No .env files found');
    } catch (e) {
        console.log('Could not list files');
    }
}

// Export environment variables with defaults
export const {
    PORT = 3000,
    NODE_ENV = nodeEnv,
    DB_URI,
    JWT_SECRET = 'fallback_jwt_secret_for_development',
    JWT_EXPIRES_IN = '1d',
    ARCJET_KEY,
    ARCJET_ENV = nodeEnv,
    QSTASH_URL = 'http://127.0.0.1:8080',
    QSTASH_TOKEN,
    QSTASH_CURRENT_SIGNING_KEY,
    QSTASH_NEXT_SIGNING_KEY,
    SERVER_URL = `http://localhost:${PORT || 3000}`,
    EMAIL_PASSWORD
} = process.env;

// Validate critical environment variables
if (!DB_URI) {
    console.error('❌ Critical Error: DB_URI environment variable is not defined!');
    console.log('📋 Current environment variables:');
    console.log(`   NODE_ENV: ${NODE_ENV}`);
    console.log(`   PORT: ${PORT}`);
    console.log(`   DB_URI: ${DB_URI ? '[DEFINED]' : '[UNDEFINED]'}`);
    console.log('\n🔧 To fix this issue:');
    console.log('1. Create an environment file (.env.development.local or .env.production.local)');
    console.log('2. Add the DB_URI variable with your database connection string');
    console.log('3. Example: DB_URI="postgresql://user:pass@host:port/database"');

    // Provide a fallback for development
    if (nodeEnv === 'development') {
        console.log('\n⚠️  Using fallback database URL for development...');
        process.env.DB_URI = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require";
    } else {
        throw new Error('Please define the DB_URI environment variable inside .env.<development/production>.local');
    }
}