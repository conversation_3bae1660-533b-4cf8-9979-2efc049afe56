---
type: "always_apply"
---

-Always try to work in current folder structure change structure if and only if it is neccassary
-make sure backend things goes to backend and frontend things go to frontend
-Always use pnpm for runing the dev command and nodemon server
-I don't want any fallbacks I want the full implementation
-If there is a bug or error please try to solve the bug or error without removing any code or modify it analyze the bug properly
-always use shadcn components
-and always use context7 mcp to get the latest docs on everything
-always make sure the code is secure and robust and you are not leaking any data ok use mcps if required to make and enhance the applications security
