#!/bin/bash

# AI Crawler Guard - VPS Startup Script
# This script is designed to work in any VPS environment

echo "🚀 AI Crawler Guard - VPS Startup Script"
echo "========================================"

# Set working directory
cd "$(dirname "$0")"
echo "📁 Working directory: $(pwd)"

# Check Node.js
if command -v node &> /dev/null; then
    echo "✅ Node.js found: $(node --version)"
else
    echo "❌ Node.js not found! Please install Node.js"
    exit 1
fi

# Check package manager
if command -v pnpm &> /dev/null; then
    PKG_MANAGER="pnpm"
    echo "✅ pnpm found: $(pnpm --version)"
elif command -v npm &> /dev/null; then
    PKG_MANAGER="npm"
    echo "✅ npm found: $(npm --version)"
else
    echo "❌ No package manager found! Please install npm or pnpm"
    exit 1
fi

# Set NODE_ENV if not set
if [ -z "$NODE_ENV" ]; then
    export NODE_ENV="development"
    echo "🔧 NODE_ENV not set, defaulting to: $NODE_ENV"
else
    echo "🔧 NODE_ENV: $NODE_ENV"
fi

# Create environment file if it doesn't exist
ENV_FILE=".env.${NODE_ENV}.local"
if [ ! -f "$ENV_FILE" ] && [ ! -f ".env" ]; then
    echo "⚠️  No environment file found. Creating $ENV_FILE..."
    
    cat > "$ENV_FILE" << 'EOF'
# AI Crawler Guard - Auto-generated Environment File
NODE_ENV=development
PORT=3000

# Database Configuration
DB_URI="postgresql://ai-crawler-guard_owner:<EMAIL>/ai-crawler-guard?sslmode=require"

# Server Configuration
SERVER_URL="http://localhost:3000"

# Payment Gateway (Test Keys)
RAZORPAY_KEY_ID="rzp_test_your_test_key_id"
RAZORPAY_KEY_SECRET="your_test_key_secret"

# WordPress Configuration
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false
WP_ENVIRONMENT=development
WORDPRESS_SITE_URL=https://creativeinteriorsstudio.com

# JWT Authentication
JWT_SECRET="vps_generated_jwt_secret_$(date +%s)"
JWT_EXPIRES_IN="1d"

# ArcJet Security
ARCJET_KEY="ajkey_01jv2k26vvercv3v13k2b9kn1d"
ARCJET_ENV="development"

# Upstash Queue Service
QSTASH_URL="http://127.0.0.1:8080"
QSTASH_TOKEN="eyJVc2VySUQiOiJkZWZhdWx0VXNlciIsIlBhc3N3b3JkIjoiZGVmYXVsdFBhc3N3b3JkIn0="

# Email Service
EMAIL_PASSWORD="ysbb ohjv ljbt ttcm"
EOF
    
    echo "✅ Created $ENV_FILE"
else
    echo "✅ Environment file found"
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    $PKG_MANAGER install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
fi

# Start the server
echo ""
echo "🚀 Starting AI Crawler Guard server..."
echo "   Environment: $NODE_ENV"
echo "   Package Manager: $PKG_MANAGER"
echo ""

# Use the appropriate start command
if [ "$PKG_MANAGER" = "pnpm" ]; then
    pnpm dev
else
    npm run dev
fi
